{"name": "budgeting-app", "version": "1.0.3", "description": "A comprehensive budgeting app built with Electron", "homepage": "https://github.com/iamthebesthackerandcoder/bugeting-app#readme", "bugs": {"url": "https://github.com/iamthebesthackerandcoder/bugeting-app/issues"}, "repository": {"type": "git", "url": "git+https://github.com/iamthebesthackerandcoder/bugeting-app.git"}, "license": "ISC", "author": {"name": "Best Hacker and Coder", "email": "<EMAIL>"}, "type": "commonjs", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "build:all": "electron-builder --win --mac --linux", "build:publish": "electron-builder --publish=always", "test": "echo \"Error: no test specified\" && exit 1", "test-token": "node test-github-token.js"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.example.my-electron-app", "productName": "Budgeting App", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "index.html", "renderer.js", "styles.css", "assets/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "publisherName": "Budgeting App Developer"}, "mac": {"target": [{"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.finance"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Office"}, "publish": {"provider": "github", "owner": "iamthebesthackerandcoder", "repo": "bugeting-app", "releaseType": "release"}}, "dependencies": {"chart.js": "^4.5.0", "dotenv": "^16.5.0", "electron-log": "^5.4.1", "electron-updater": "^6.6.2"}}