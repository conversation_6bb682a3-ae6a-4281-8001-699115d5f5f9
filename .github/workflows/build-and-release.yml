name: Build and Release

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux
            build_cmd: npm run build:linux
          - os: windows-latest
            platform: windows
            build_cmd: npm run build:win
          - os: macos-latest
            platform: macos
            build_cmd: npm run build:mac

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: ${{ matrix.build_cmd }}
      env:
        GH_TOKEN: ${{ secrets.GH_TOKEN || secrets.GITHUB_TOKEN }}
        
    - name: Upload artifacts (Linux)
      if: matrix.platform == 'linux'
      uses: actions/upload-artifact@v4
      with:
        name: linux-builds
        path: |
          dist/*.AppImage
          dist/*.deb
          dist/latest-linux.yml

    - name: Upload artifacts (Windows)
      if: matrix.platform == 'windows'
      uses: actions/upload-artifact@v4
      with:
        name: windows-builds
        path: |
          dist/*.exe
          dist/*.exe.blockmap
          dist/latest.yml

    - name: Upload artifacts (macOS)
      if: matrix.platform == 'macos'
      uses: actions/upload-artifact@v4
      with:
        name: macos-builds
        path: |
          dist/*.zip
          dist/*.zip.blockmap
          dist/latest-mac.yml

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          linux-builds/*
          windows-builds/*
          macos-builds/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GH_TOKEN || secrets.GITHUB_TOKEN }}
